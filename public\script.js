document.addEventListener('DOMContentLoaded', () => {
    // 获取DOM元素
    const statusDisplay = document.getElementById('status');
    const connectionsDisplay = document.getElementById('connections');
    
    // 定义三个文本区域
    const textAreas = [
        {
            input: document.getElementById('text-input-1'),
            copyBtn: document.getElementById('copy-btn-1'),
            clearBtn: document.getElementById('clear-btn-1'),
            id: 'text-1'
        },
        {
            input: document.getElementById('text-input-2'),
            copyBtn: document.getElementById('copy-btn-2'),
            clearBtn: document.getElementById('clear-btn-2'),
            id: 'text-2'
        },
        {
            input: document.getElementById('text-input-3'),
            copyBtn: document.getElementById('copy-btn-3'),
            clearBtn: document.getElementById('clear-btn-3'),
            id: 'text-3'
        }
    ];
    
    // 连接到WebSocket服务器
    const socket = io();
    let connectionCount = 1;
    let typingStates = {
        'text-1': false,
        'text-2': false,
        'text-3': false
    };
    let typingTimers = {
        'text-1': null,
        'text-2': null,
        'text-3': null
    };
    
    // 连接建立
    socket.on('connect', () => {
        statusDisplay.textContent = '状态: 已连接';
        statusDisplay.style.color = '#2ecc71';
    });
    
    // 连接断开
    socket.on('disconnect', () => {
        statusDisplay.textContent = '状态: 已断开连接';
        statusDisplay.style.color = '#e74c3c';
    });
    
    // 接收连接数更新
    socket.on('connection-count', (count) => {
        connectionCount = count;
        connectionsDisplay.textContent = `连接数: ${connectionCount}`;
    });
    
    // 为每个文本区域设置事件处理
    textAreas.forEach(area => {
        // 接收文本更新
        socket.on(`text-update-${area.id}`, (text) => {
            // 只有当用户没有正在输入时才更新文本
            if (!typingStates[area.id]) {
                area.input.value = text;
            }
        });
        
        // 输入文本时发送更新
        area.input.addEventListener('input', () => {
            // 标记正在输入状态
            typingStates[area.id] = true;
            clearTimeout(typingTimers[area.id]);
            
            // 发送文本到服务器
            socket.emit(`text-update-${area.id}`, area.input.value);
            
            // 设置输入结束的计时器
            typingTimers[area.id] = setTimeout(() => {
                typingStates[area.id] = false;
            }, 1000);
        });
        
        // 复制按钮功能 - 修复聚焦问题
        area.copyBtn.addEventListener('click', () => {
            if (area.input.value.trim() === '') {
                alert('没有可复制的文本');
                return;
            }
            
            // 使用新的复制方法，避免选择文本
            const textToCopy = area.input.value;
            copyTextToClipboard(textToCopy, area.copyBtn);
        });
        
        // 清空按钮功能 - 无需确认直接清空
        area.clearBtn.addEventListener('click', () => {
            area.input.value = '';
            socket.emit(`text-update-${area.id}`, '');
        });
        
        // 处理焦点事件以防止输入冲突
        area.input.addEventListener('focus', () => {
            typingStates[area.id] = true;
        });
        
        area.input.addEventListener('blur', () => {
            typingStates[area.id] = false;
        });
    });
    
    // 检测是否为老版本iOS Safari
    function isOldIOSSafari() {
        const ua = navigator.userAgent;
        const isIOS = /iPad|iPhone|iPod/.test(ua);
        const isOldIOS = /OS [1-9]_|OS 1[0-2]_/.test(ua); // iOS 12及以下
        const isSafari = /Safari/.test(ua) && !/Chrome/.test(ua);
        return isIOS && isOldIOS && isSafari;
    }

    // 复制文本到剪贴板的通用函数
    function copyTextToClipboard(text, button) {
        // 对于老版本iOS Safari，直接使用改进的后备方法
        if (isOldIOSSafari()) {
            improvedFallbackCopyMethod(text, button);
            return;
        }

        // 优先使用现代剪贴板API
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(text)
                .then(() => {
                    showCopySuccess(button);
                })
                .catch(err => {
                    console.error('现代API复制失败:', err);
                    improvedFallbackCopyMethod(text, button);
                });
        } else {
            improvedFallbackCopyMethod(text, button);
        }
    }

    // 改进的后备复制方法，专门针对老版本Safari优化
    function improvedFallbackCopyMethod(text, button) {
        // 创建一个临时的textarea元素
        const textArea = document.createElement('textarea');
        textArea.value = text;

        // 针对iOS Safari的特殊样式设置
        textArea.style.position = 'absolute';
        textArea.style.left = '-9999px';
        textArea.style.top = '0';
        textArea.style.opacity = '0';
        textArea.style.pointerEvents = 'none';
        textArea.style.zIndex = '-1';

        // 设置为只读，防止键盘弹出
        textArea.readOnly = true;

        document.body.appendChild(textArea);

        // 针对iOS的特殊处理
        if (/iPad|iPhone|iPod/.test(navigator.userAgent)) {
            // iOS需要特殊的选择方法
            const range = document.createRange();
            range.selectNodeContents(textArea);
            const selection = window.getSelection();
            selection.removeAllRanges();
            selection.addRange(range);
            textArea.setSelectionRange(0, 999999);
        } else {
            // 其他设备的标准方法
            textArea.focus();
            textArea.select();
        }

        let successful = false;
        try {
            successful = document.execCommand('copy');
        } catch (err) {
            console.error('execCommand复制失败:', err);
            successful = false;
        }

        // 清理
        document.body.removeChild(textArea);

        if (successful) {
            showCopySuccess(button);
        } else {
            // 如果还是失败，提供手动复制的提示
            showManualCopyDialog(text, button);
        }
    }

    // 显示手动复制对话框
    function showManualCopyDialog(text, button) {
        const dialog = document.createElement('div');
        dialog.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border: 2px solid #ccc;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            z-index: 10000;
            max-width: 90%;
            max-height: 80%;
        `;

        dialog.innerHTML = `
            <h3 style="margin-top: 0;">请手动复制以下内容：</h3>
            <textarea readonly style="width: 100%; height: 150px; margin: 10px 0; padding: 8px; border: 1px solid #ccc; border-radius: 4px;">${text}</textarea>
            <div style="text-align: center;">
                <button onclick="this.parentElement.parentElement.remove()" style="padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">关闭</button>
            </div>
        `;

        document.body.appendChild(dialog);

        // 自动选择文本
        const textarea = dialog.querySelector('textarea');
        textarea.focus();
        textarea.select();

        // 5秒后自动关闭
        setTimeout(() => {
            if (dialog.parentElement) {
                dialog.remove();
            }
        }, 10000);
    }
    
    // 显示复制成功的视觉反馈
    function showCopySuccess(button) {
        const originalText = button.textContent;
        button.textContent = '已复制!';
        button.style.backgroundColor = '#2ecc71';
        
        setTimeout(() => {
            button.textContent = originalText;
            button.style.backgroundColor = '';
        }, 1500);
    }
}); 