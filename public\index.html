<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>局域网文本共享</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <h1>局域网文本共享</h1>
        
        <div class="status-container">
            <div id="status">状态: 已连接</div>
            <div id="connections">连接数: 1</div>
        </div>
        
        <!-- 第一个输入区域 -->
        <div class="text-section">
            <h2>文本区域 1</h2>
            <div class="input-container">
                <textarea id="text-input-1" class="text-input" placeholder="在此处输入文本，将实时同步到局域网中的其他设备..."></textarea>
            </div>
            
            <div class="button-container">
                <button id="copy-btn-1" class="copy-btn">复制文本</button>
                <button id="clear-btn-1" class="clear-btn">清空文本</button>
            </div>
        </div>
        
        <!-- 第二个输入区域 -->
        <div class="text-section">
            <h2>文本区域 2</h2>
            <div class="input-container">
                <textarea id="text-input-2" class="text-input" placeholder="在此处输入文本，将实时同步到局域网中的其他设备..."></textarea>
            </div>
            
            <div class="button-container">
                <button id="copy-btn-2" class="copy-btn">复制文本</button>
                <button id="clear-btn-2" class="clear-btn">清空文本</button>
            </div>
        </div>
        
        <!-- 第三个输入区域 -->
        <div class="text-section">
            <h2>文本区域 3</h2>
            <div class="input-container">
                <textarea id="text-input-3" class="text-input" placeholder="在此处输入文本，将实时同步到局域网中的其他设备..."></textarea>
            </div>
            
            <div class="button-container">
                <button id="copy-btn-3" class="copy-btn">复制文本</button>
                <button id="clear-btn-3" class="clear-btn">清空文本</button>
            </div>
        </div>
        
        <div class="info">
            <p>使用说明:</p>
            <ul>
                <li>输入的文本会自动同步到局域网内访问此页面的所有设备</li>
                <li>每个文本区域都是独立的，可以分别输入不同内容</li>
                <li>点击对应的"复制文本"按钮可以复制该区域内容到剪贴板</li>
                <li>点击对应的"清空文本"按钮将清除该区域内容</li>
            </ul>
        </div>
    </div>

    <script src="/socket.io/socket.io.js"></script>
    <script src="script.js"></script>
</body>
</html> 