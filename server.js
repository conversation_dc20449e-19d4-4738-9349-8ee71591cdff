const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');
const os = require('os');

// 创建Express应用
const app = express();
const server = http.createServer(app);
const io = socketIo(server);

// 提供静态文件
app.use(express.static(path.join(__dirname, 'public')));

// 存储当前共享的文本 - 三个独立区域
let sharedTexts = {
  'text-1': '',
  'text-2': '',
  'text-3': ''
};

// 连接计数
let connectionCount = 0;

// WebSocket连接处理
io.on('connection', (socket) => {
  connectionCount++;
  console.log(`新客户端已连接 (当前连接数: ${connectionCount})`);
  
  // 发送当前文本给新连接的客户端
  socket.emit('text-update-text-1', sharedTexts['text-1']);
  socket.emit('text-update-text-2', sharedTexts['text-2']);
  socket.emit('text-update-text-3', sharedTexts['text-3']);
  
  // 发送更新的连接数给所有客户端
  io.emit('connection-count', connectionCount);
  
  // 监听各个文本区域的更新事件
  socket.on('text-update-text-1', (text) => {
    sharedTexts['text-1'] = text;
    // 广播给所有其他客户端
    socket.broadcast.emit('text-update-text-1', text);
  });
  
  socket.on('text-update-text-2', (text) => {
    sharedTexts['text-2'] = text;
    // 广播给所有其他客户端
    socket.broadcast.emit('text-update-text-2', text);
  });
  
  socket.on('text-update-text-3', (text) => {
    sharedTexts['text-3'] = text;
    // 广播给所有其他客户端
    socket.broadcast.emit('text-update-text-3', text);
  });
  
  // 断开连接处理
  socket.on('disconnect', () => {
    connectionCount--;
    console.log(`客户端已断开连接 (当前连接数: ${connectionCount})`);
    io.emit('connection-count', connectionCount);
  });
});

// 获取本地IP地址
function getLocalIp() {
  const interfaces = os.networkInterfaces();
  let wlanIp = null;
  
  // 首先尝试查找无线网卡(WLAN)
  if (interfaces['WLAN']) {
    for (const iface of interfaces['WLAN']) {
      if (iface.family === 'IPv4' && !iface.internal) {
        return iface.address; // 优先返回WLAN接口的IP
      }
    }
  }
  
  // 查找任何其他非虚拟的网络接口
  for (const name of Object.keys(interfaces)) {
    // 跳过VMware虚拟网卡
    if (name.includes('VMware') || name.includes('VMnet')) {
      continue;
    }
    
    for (const iface of interfaces[name]) {
      if (iface.family === 'IPv4' && !iface.internal) {
        return iface.address;
      }
    }
  }
  
  // 如果没有找到合适的接口，再查找任何非本地IPv4地址
  for (const name of Object.keys(interfaces)) {
    for (const iface of interfaces[name]) {
      if (iface.family === 'IPv4' && !iface.internal) {
        return iface.address;
      }
    }
  }
  
  return 'localhost';
}

// 启动服务器
const PORT = process.env.PORT || 3000;
server.listen(PORT, '0.0.0.0', () => {
  const localIp = getLocalIp();
  console.log(`服务器运行在 http://localhost:${PORT}`);
  console.log(`局域网内其他设备可访问: http://${localIp}:${PORT}`);
}); 