* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f7f7f7;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 900px;
    margin: 20px auto;
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

h1 {
    text-align: center;
    margin-bottom: 20px;
    color: #2c3e50;
}

h2 {
    font-size: 18px;
    margin-bottom: 10px;
    color: #2c3e50;
    border-bottom: 1px solid #eee;
    padding-bottom: 5px;
}

.status-container {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    font-size: 14px;
    color: #666;
}

.text-section {
    margin-bottom: 30px;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 6px;
    border-left: 4px solid #3498db;
}

.input-container {
    margin-bottom: 10px;
}

.text-input {
    width: 100%;
    height: 60px;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    resize: vertical;
    font-size: 16px;
    font-family: inherit;
    transition: border-color 0.3s;
}

.text-input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 5px rgba(52, 152, 219, 0.3);
}

.button-container {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
}

button {
    padding: 8px 12px;
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s;
}

button:hover {
    background-color: #2980b9;
}

.clear-btn {
    background-color: #e74c3c;
}

.clear-btn:hover {
    background-color: #c0392b;
}

.copy-btn {
    background-color: #2ecc71;
}

.copy-btn:hover {
    background-color: #27ae60;
}

.info {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 4px;
    border-left: 4px solid #3498db;
    margin-top: 20px;
}

.info p {
    font-weight: bold;
    margin-bottom: 5px;
}

.info ul {
    padding-left: 20px;
}

.info li {
    margin-bottom: 5px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        margin: 10px;
        padding: 15px;
    }
    
    .text-input {
        height: 50px;
    }
    
    h1 {
        font-size: 22px;
    }
    
    .text-section {
        padding: 10px;
        margin-bottom: 20px;
    }
}

@media (max-width: 480px) {
    .button-container {
        flex-direction: column;
        gap: 5px;
    }
    
    button {
        width: 100%;
    }
} 